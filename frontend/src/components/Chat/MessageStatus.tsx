// frontend/src/components/Chat/MessageStatus.tsx
import React from 'react';
import { Icon } from '../ui/Icon';
import type { MessageStatusType } from '../../store/slices/messageSlice';

interface MessageStatusProps {
  status?: MessageStatusType;
  isOwnMessage: boolean;
  isSending?: boolean;
  onRetry?: () => void;
  className?: string;
  isEncrypted?: boolean;
  encryptionFailed?: boolean;
}

const MessageStatus: React.FC<MessageStatusProps> = ({
  status,
  isOwnMessage,
  isSending = false,
  onRetry,
  className = '',
  isEncrypted = false,
  encryptionFailed = false
}) => {
  // Only show status for own messages
  if (!isOwnMessage) {
    return null;
  }

  // Show loading indicator when sending
  if (isSending) {
    return (
      <div className={`flex items-center space-x-1 text-xs ${className}`}>
        <Icon name="loader" size={12} className="animate-spin opacity-70" />
        <span className="opacity-70">Sending...</span>
      </div>
    );
  }

  // Show status indicators based on message status
  const renderStatusIcon = () => {
    switch (status) {
      case 'SENT':
        return <Icon name="check" size={12} className="text-gray-400" />;
      case 'DELIVERED':
        return <Icon name="check" size={12} className="text-gray-500" />;
      case 'READ':
        return <Icon name="check-check" size={12} className="text-green-500" />;
      case 'FAILED':
        return onRetry ? (
          <button
            onClick={onRetry}
            className="flex items-center space-x-1 text-red-500 hover:text-red-600 transition-colors"
            title="Retry sending message"
          >
            <Icon name="refresh-cw" size={12} />
            <span className="text-xs">Retry</span>
          </button>
        ) : (
          <Icon name="alert-circle" size={12} className="text-red-500" title="Failed to send" />
        );
      default:
        return null;
    }
  };

  // Show encryption status indicator
  const renderEncryptionIcon = () => {
    if (encryptionFailed) {
      return (
        <Icon
          name="shield-off"
          size={12}
          className="text-red-500"
          title="Encryption failed - message not sent"
        />
      );
    }

    if (isEncrypted) {
      return (
        <Icon
          name="shield"
          size={12}
          className="text-green-600"
          title="Message encrypted"
        />
      );
    }

    return null;
  };

  const statusIcon = renderStatusIcon();
  const encryptionIcon = renderEncryptionIcon();

  // If no icons to show, return null
  if (!statusIcon && !encryptionIcon) {
    return null;
  }

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {encryptionIcon}
      {statusIcon}
    </div>
  );
};

export default MessageStatus;
