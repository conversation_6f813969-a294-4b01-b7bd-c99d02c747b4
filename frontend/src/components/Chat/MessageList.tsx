// frontend/src/components/Chat/MessageList.tsx
import React, { useEffect, useRef, useMemo, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { formatDistanceToNow } from 'date-fns';
import { Icon } from '../ui/Icon';
import MessageStatus from './MessageStatus';
import { useSocket } from '../../contexts/SocketContext';
import { useEncryption } from '../../contexts/EncryptionContext';
import { useGetMessagesQuery } from '../../services/messageApi';
import type { RootState } from '../../store';
import type { Message } from '../../services/messageApi';

interface MessageListProps {
  conversationId: string;
  currentUserId: string;
  onReply?: (message: Message) => void;
}

const MessageList: React.FC<MessageListProps> = ({
  conversationId,
  currentUserId,
  onReply
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Check if this is a draft conversation
  const isDraftConversation = conversationId?.startsWith('draft-');

  // Use RTK Query to get messages - skip for draft conversations
  const { data: messagesData, isLoading, error } = useGetMessagesQuery(
    { conversationId },
    { skip: !conversationId || isDraftConversation }
  );



  // Still use Redux store for sending states and typing users (real-time features)
  const sendingMessages = useSelector((state: RootState) => state.messages.sendingMessages || {});
  const typingUsersState = useSelector((state: RootState) => state.messages.typingUsers || {});
  const messageStatuses = useSelector((state: RootState) => state.messages.messageStatuses || {});
  const failedMessages = useSelector((state: RootState) => state.messages.failedMessages || {});
  const reduxMessages = useSelector((state: RootState) => state.messages.messages[conversationId] || []);

  const { retryFailedMessage, markMessageAsRead } = useSocket();
  const { decryptMessage, isInitialized: encryptionInitialized } = useEncryption();

  // Process raw messages (combine RTK Query and Redux)
  const rawMessages = useMemo(() => {
    // For draft conversations, use Redux store messages (includes optimistic updates)
    if (isDraftConversation) {
      return [...reduxMessages].sort((a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );
    }

    // For real conversations, combine RTK Query data with Redux messages
    // This ensures we don't lose messages during the transition
    const rtkMessages = messagesData?.results || [];
    const allMessages = [...rtkMessages];

    // Add any Redux messages that might not be in RTK Query yet
    reduxMessages.forEach(reduxMsg => {
      const existsInRtk = rtkMessages.some(rtkMsg =>
        rtkMsg.id === reduxMsg.id ||
        (rtkMsg.content === reduxMsg.content &&
         rtkMsg.sender.id === reduxMsg.sender.id &&
         Math.abs(new Date(rtkMsg.createdAt).getTime() - new Date(reduxMsg.createdAt).getTime()) < 10000)
      );

      if (!existsInRtk) {
        allMessages.push(reduxMsg);
      }
    });

    return allMessages.sort((a, b) =>
      new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );
  }, [messagesData, reduxMessages, isDraftConversation, conversationId, isLoading, error]);

  // Process messages with decryption using useMemo to avoid infinite loops
  const messages = useMemo(() => {
    // If encryption is not initialized or no messages, return raw messages
    if (!encryptionInitialized || rawMessages.length === 0) {
      return rawMessages;
    }

    // For now, return raw messages and handle decryption in a separate optimization
    // This prevents the infinite loop while maintaining functionality
    return rawMessages.map((message) => {
      // If message has encrypted content but no decrypted content, show placeholder
      if (message.encryptedContent && message.iv && !message.content) {
        return {
          ...message,
          content: '[Encrypted message - decryption in progress...]',
        };
      }

      // Return message as-is (either unencrypted or already has content)
      return message;
    });
  }, [rawMessages, encryptionInitialized]);

  const typingUsers = useMemo(() =>
    typingUsersState[conversationId] || [],
    [typingUsersState, conversationId]
  );

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Mark messages as read when they come into view
  const handleMessageVisible = useCallback((messageId: string, senderId: string) => {
    // Only mark as read if it's not our own message and not already read
    if (senderId !== currentUserId && messageStatuses[messageId] !== 'READ') {
      markMessageAsRead(messageId);
    }
  }, [currentUserId, messageStatuses, markMessageAsRead]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Set up intersection observer for read receipts
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const messageElement = entry.target as HTMLElement;
            const messageId = messageElement.dataset.messageId;
            const senderId = messageElement.dataset.senderId;

            if (messageId && senderId) {
              handleMessageVisible(messageId, senderId);
            }
          }
        });
      },
      {
        threshold: 0.5, // Message is considered "read" when 50% visible
        rootMargin: '0px'
      }
    );

    // Observe all message elements
    const messageElements = document.querySelectorAll('[data-message-id]');
    messageElements.forEach((element) => observer.observe(element));

    return () => {
      observer.disconnect();
    };
  }, [messages, handleMessageVisible]);

  const getInitials = (first_name?: string, last_name?: string) => {
    if (!first_name && !last_name) return '?';
    return `${first_name?.[0] || ''}${last_name?.[0] || ''}`.toUpperCase() || '?';
  };

  const isMessageSending = (messageId: string) => {
    return sendingMessages[messageId] || false;
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center text-gray-500">
          <Icon name="loader" size={48} className="mx-auto mb-4 text-gray-300 animate-spin" />
          <p className="text-lg font-medium mb-2">Loading messages...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center text-red-500">
          <Icon name="alert-circle" size={48} className="mx-auto mb-4 text-red-300" />
          <p className="text-lg font-medium mb-2">Failed to load messages</p>
          <p className="text-sm">Please try refreshing the page</p>
        </div>
      </div>
    );
  }

  // Empty state
  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center text-gray-500">
          <Icon name="message-circle" size={48} className="mx-auto mb-4 text-gray-300" />
          <p className="text-lg font-medium mb-2">No messages yet</p>
          <p className="text-sm">Start the conversation by sending a message</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-auto p-4 space-y-4" data-testid="message-list">
      {messages.map((message) => {
        const isOwnMessage = message.sender.id === currentUserId;
        const isSending = isMessageSending(message.id);

        return (
          <div
            key={message.id}
            className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
            data-message-id={message.id}
            data-sender-id={message.sender.id}
            data-testid="message"
          >
            <div className={`flex max-w-[70%] items-end space-x-2 ${isOwnMessage ? 'flex-row-reverse space-x-reverse' : ''}`}>
              {!isOwnMessage && (
                <div className="flex-shrink-0">
                  {message.sender.profile_picture ? (
                    <img
                      src={message.sender.profile_picture}
                      alt={`${message.sender.first_name} ${message.sender.last_name}`}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-gray-400 flex items-center justify-center text-white text-sm font-medium">
                      {getInitials(message.sender.first_name, message.sender.last_name)}
                    </div>
                  )}
                </div>
              )}

              <div
                className={`relative group rounded-lg px-4 py-2 max-w-full ${
                  isOwnMessage
                    ? 'bg-blue-600 text-white rounded-br-none'
                    : 'bg-gray-100 text-gray-900 rounded-bl-none'
                } ${isSending ? 'opacity-70' : ''}`}
              >
                <div className="text-sm leading-relaxed">
                  {message.content}
                </div>

                <div className={`flex items-center justify-between mt-1 ${
                  isOwnMessage ? 'text-blue-100' : 'text-gray-500'
                }`}>
                  <div className="text-xs opacity-70">
                    {message.createdAt
                      ? formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })
                      : 'No date'}
                  </div>

                  <MessageStatus
                    status={messageStatuses[message.id]}
                    isOwnMessage={isOwnMessage}
                    isSending={isSending}
                    onRetry={failedMessages[message.id] ? () => {
                      retryFailedMessage(message.id, conversationId, message.content, message.messageType);
                    } : undefined}
                    className={isOwnMessage ? 'text-blue-100' : 'text-gray-500'}
                  />
                </div>

                {/* Message actions */}
                {onReply && !isSending && (
                  <button
                    onClick={() => onReply(message)}
                    className={`absolute top-0 right-0 transform translate-x-8 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded-full ${
                      isOwnMessage ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-600'
                    } hover:bg-opacity-80`}
                    title="Reply to message"
                  >
                    <Icon name="message-circle" size={14} />
                  </button>
                )}
              </div>
            </div>
          </div>
        );
      })}

      {/* Typing indicators */}
      {typingUsers.length > 0 && (
        <div className="flex justify-start">
          <div className="bg-gray-100 rounded-lg px-4 py-2 text-gray-600 text-sm">
            <div className="flex items-center space-x-2">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span>
                {typingUsers.length === 1 ? 'Someone is' : `${typingUsers.length} people are`} typing...
              </span>
            </div>
          </div>
        </div>
      )}

      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
