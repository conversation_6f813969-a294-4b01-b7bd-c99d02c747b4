// frontend/src/utils/crypto.ts
export class CryptoUtils {
  private static readonly ALGORITHM = 'AES-GCM';
  private static readonly KEY_LENGTH = 256;
  private static readonly IV_LENGTH = 12;

  /**
   * Generate a new X25519 key pair for identity or ephemeral keys
   */
  static async generateKeyPair(): Promise<CryptoKeyPair> {
    return await window.crypto.subtle.generateKey(
      {
        name: 'X25519',
      },
      true, // extractable
      ['deriveKey', 'deriveBits']
    ) as CryptoKeyPair;
  }

  /**
   * Generate a signing key pair for signed pre-keys (Ed25519)
   */
  static async generateSigningKeyPair(): Promise<CryptoKeyPair> {
    return await window.crypto.subtle.generateKey(
      {
        name: 'Ed25519',
      },
      true,
      ['sign', 'verify']
    ) as CryptoKeyPair;
  }

  /**
   * Export public key to base64 string
   */
  static async exportPublicKey(key: Crypto<PERSON><PERSON>): Promise<string> {
    const exported = await window.crypto.subtle.exportKey('spki', key);
    return btoa(String.fromCharCode(...new Uint8Array(exported)));
  }

  /**
   * Export private key to base64 string
   */
  static async exportPrivateKey(key: CryptoKey): Promise<string> {
    const exported = await window.crypto.subtle.exportKey('pkcs8', key);
    return btoa(String.fromCharCode(...new Uint8Array(exported)));
  }

  /**
   * Import public key from base64 string
   */
  static async importPublicKey(keyData: string, algorithm: string): Promise<CryptoKey> {
    const binaryKey = Uint8Array.from(atob(keyData), c => c.charCodeAt(0));
    
    let keyAlgorithm;
    let keyUsages: KeyUsage[];
    
    if (algorithm === 'X25519') {
      keyAlgorithm = { name: 'X25519' };
      keyUsages = ['deriveKey', 'deriveBits'];
    } else if (algorithm === 'Ed25519') {
      keyAlgorithm = { name: 'Ed25519' };
      keyUsages = ['verify'];
    } else {
      throw new Error(`Unsupported algorithm: ${algorithm}`);
    }
    
    return await window.crypto.subtle.importKey(
      'spki',
      binaryKey,
      keyAlgorithm,
      false,
      keyUsages
    );
  }

  /**
   * Import private key from base64 string
   */
  static async importPrivateKey(keyData: string, algorithm: string): Promise<CryptoKey> {
    const binaryKey = Uint8Array.from(atob(keyData), c => c.charCodeAt(0));
    
    let keyAlgorithm;
    let keyUsages: KeyUsage[];
    
    if (algorithm === 'X25519') {
      keyAlgorithm = { name: 'X25519' };
      keyUsages = ['deriveKey', 'deriveBits'];
    } else if (algorithm === 'Ed25519') {
      keyAlgorithm = { name: 'Ed25519' };
      keyUsages = ['sign'];
    } else {
      throw new Error(`Unsupported algorithm: ${algorithm}`);
    }
    
    return await window.crypto.subtle.importKey(
      'pkcs8',
      binaryKey,
      keyAlgorithm,
      true,
      keyUsages
    );
  }

  /**
   * Derive shared secret from X25519 key pair
   */
  static async deriveSharedSecret(privateKey: CryptoKey, publicKey: CryptoKey): Promise<ArrayBuffer> {
    return await window.crypto.subtle.deriveBits(
      {
        name: 'X25519',
        public: publicKey,
      },
      privateKey,
      256
    );
  }

  /**
   * Derive encryption key from shared secret using HKDF
   */
  static async deriveEncryptionKey(sharedSecret: ArrayBuffer, salt: Uint8Array, info?: string): Promise<CryptoKey> {
    const keyMaterial = await window.crypto.subtle.importKey(
      'raw',
      sharedSecret,
      'HKDF',
      false,
      ['deriveKey']
    );

    return await window.crypto.subtle.deriveKey(
      {
        name: 'HKDF',
        hash: 'SHA-256',
        salt: salt,
        info: new TextEncoder().encode(info || 'ChatApp Message Key'),
      },
      keyMaterial,
      { name: this.ALGORITHM, length: this.KEY_LENGTH },
      false,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Encrypt message content using AES-256-GCM
   */
  static async encryptMessage(content: string, key: CryptoKey): Promise<{
    encryptedData: string;
    iv: string;
  }> {
    const iv = window.crypto.getRandomValues(new Uint8Array(this.IV_LENGTH));
    const encodedContent = new TextEncoder().encode(content);

    const encryptedBuffer = await window.crypto.subtle.encrypt(
      {
        name: this.ALGORITHM,
        iv: iv,
      },
      key,
      encodedContent
    );

    return {
      encryptedData: btoa(String.fromCharCode(...new Uint8Array(encryptedBuffer))),
      iv: btoa(String.fromCharCode(...iv)),
    };
  }

  /**
   * Decrypt message content using AES-256-GCM
   */
  static async decryptMessage(encryptedData: string, iv: string, key: CryptoKey): Promise<string> {
    const encryptedBuffer = Uint8Array.from(atob(encryptedData), c => c.charCodeAt(0));
    const ivBuffer = Uint8Array.from(atob(iv), c => c.charCodeAt(0));

    const decryptedBuffer = await window.crypto.subtle.decrypt(
      {
        name: this.ALGORITHM,
        iv: ivBuffer,
      },
      key,
      encryptedBuffer
    );

    return new TextDecoder().decode(decryptedBuffer);
  }

  /**
   * Sign data using Ed25519
   */
  static async signData(data: string | Uint8Array, privateKey: CryptoKey): Promise<string> {
    const dataToSign = typeof data === 'string' ? new TextEncoder().encode(data) : data;
    
    const signature = await window.crypto.subtle.sign(
      'Ed25519',
      privateKey,
      dataToSign
    );

    return btoa(String.fromCharCode(...new Uint8Array(signature)));
  }

  /**
   * Verify signature using Ed25519
   */
  static async verifySignature(data: string | Uint8Array, signature: string, publicKey: CryptoKey): Promise<boolean> {
    const dataToVerify = typeof data === 'string' ? new TextEncoder().encode(data) : data;
    const signatureBuffer = Uint8Array.from(atob(signature), c => c.charCodeAt(0));

    return await window.crypto.subtle.verify(
      'Ed25519',
      publicKey,
      signatureBuffer,
      dataToVerify
    );
  }

  /**
   * Generate random bytes
   */
  static generateRandomBytes(length: number): Uint8Array {
    return window.crypto.getRandomValues(new Uint8Array(length));
  }

  /**
   * Hash data using SHA-256
   */
  static async hash(data: string | Uint8Array): Promise<string> {
    const dataToHash = typeof data === 'string' ? new TextEncoder().encode(data) : data;
    const hashBuffer = await window.crypto.subtle.digest('SHA-256', dataToHash);
    return btoa(String.fromCharCode(...new Uint8Array(hashBuffer)));
  }

  /**
   * Derive key from password using PBKDF2
   */
  static async deriveKeyFromPassword(password: string, salt: Uint8Array, iterations: number = 100000): Promise<CryptoKey> {
    const keyMaterial = await window.crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode(password),
      'PBKDF2',
      false,
      ['deriveKey']
    );

    return await window.crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: salt,
        iterations: iterations,
        hash: 'SHA-256',
      },
      keyMaterial,
      { name: this.ALGORITHM, length: this.KEY_LENGTH },
      false,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Combine multiple shared secrets using HKDF
   */
  static async combineSharedSecrets(secrets: ArrayBuffer[], salt?: Uint8Array): Promise<ArrayBuffer> {
    // Concatenate all secrets
    const totalLength = secrets.reduce((acc, secret) => acc + secret.byteLength, 0);
    const combined = new Uint8Array(totalLength);
    let offset = 0;
    
    for (const secret of secrets) {
      combined.set(new Uint8Array(secret), offset);
      offset += secret.byteLength;
    }

    // Use HKDF to derive a proper key from the combined secrets
    const keyMaterial = await window.crypto.subtle.importKey(
      'raw',
      combined,
      'HKDF',
      false,
      ['deriveBits']
    );

    return await window.crypto.subtle.deriveBits(
      {
        name: 'HKDF',
        hash: 'SHA-256',
        salt: salt || new Uint8Array(32), // Default salt if none provided
        info: new TextEncoder().encode('Combined Shared Secret'),
      },
      keyMaterial,
      256
    );
  }

  /**
   * Constant-time comparison of two arrays
   */
  static constantTimeEqual(a: Uint8Array, b: Uint8Array): boolean {
    if (a.length !== b.length) {
      return false;
    }
    
    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a[i] ^ b[i];
    }
    
    return result === 0;
  }
}
