// frontend/src/utils/signalProtocol.ts
import { CryptoUtils } from './crypto';

export interface KeyBundle {
  identityKey: string;
  signedPreKey: {
    id: number;
    publicKey: string;
    signature: string;
  };
  oneTimePreKey?: {
    id: number;
    publicKey: string;
  };
}

export interface SessionState {
  rootKey: string;
  chainKeySend?: string;
  chainKeyReceive?: string;
  messageNumberSend: number;
  messageNumberReceive: number;
  previousChainLength: number;
  ratchetKeyPair?: {
    private: string;
    public: string;
  };
  remoteRatchetKey?: string;
}

export interface EncryptedMessage {
  encryptedContent: string;
  iv: string;
  messageNumber: number;
  ratchetKey: string;
  previousChainLength: number;
}

export class SignalProtocol {
  private identityKeyPair: CryptoKeyPair | null = null;
  private signedPreKeyPair: CryptoKeyPair | null = null;
  private signingKeyPair: CryptoKeyPair | null = null;
  private oneTimePreKeys: Map<number, CryptoKeyPair> = new Map();
  private sessions: Map<string, SessionState> = new Map();

  /**
   * Initialize user's key pairs
   */
  async initializeKeys(): Promise<void> {
    // Generate identity key pair (X25519)
    this.identityKeyPair = await CryptoUtils.generateKeyPair();
    
    // Generate signing key pair (Ed25519) for signing pre-keys
    this.signingKeyPair = await CryptoUtils.generateSigningKeyPair();
    
    // Generate signed pre-key pair (X25519)
    this.signedPreKeyPair = await CryptoUtils.generateKeyPair();
    
    // Generate one-time pre-keys (X25519)
    for (let i = 0; i < 100; i++) {
      const keyPair = await CryptoUtils.generateKeyPair();
      this.oneTimePreKeys.set(i, keyPair);
    }
  }

  /**
   * Get user's public key bundle for sharing
   */
  async getKeyBundle(): Promise<KeyBundle> {
    if (!this.identityKeyPair || !this.signedPreKeyPair || !this.signingKeyPair) {
      throw new Error('Keys not initialized');
    }

    const identityPublicKey = await CryptoUtils.exportPublicKey(this.identityKeyPair.publicKey);
    const signedPreKeyPublic = await CryptoUtils.exportPublicKey(this.signedPreKeyPair.publicKey);
    
    // Sign the pre-key with identity signing key
    const signature = await CryptoUtils.signData(signedPreKeyPublic, this.signingKeyPair.privateKey);

    // Get a one-time pre-key
    const oneTimePreKeyId = Array.from(this.oneTimePreKeys.keys())[0];
    const oneTimePreKeyPair = this.oneTimePreKeys.get(oneTimePreKeyId);
    let oneTimePreKey;
    
    if (oneTimePreKeyPair) {
      oneTimePreKey = {
        id: oneTimePreKeyId,
        publicKey: await CryptoUtils.exportPublicKey(oneTimePreKeyPair.publicKey),
      };
    }

    return {
      identityKey: identityPublicKey,
      signedPreKey: {
        id: 1,
        publicKey: signedPreKeyPublic,
        signature: signature,
      },
      oneTimePreKey,
    };
  }

  /**
   * Initialize session with remote user's key bundle
   */
  async initializeSession(conversationId: string, remoteKeyBundle: KeyBundle): Promise<SessionState> {
    if (!this.identityKeyPair) {
      throw new Error('Identity key not initialized');
    }

    // Import remote keys
    const remoteIdentityKey = await CryptoUtils.importPublicKey(remoteKeyBundle.identityKey, 'X25519');
    const remoteSignedPreKey = await CryptoUtils.importPublicKey(remoteKeyBundle.signedPreKey.publicKey, 'X25519');
    
    let remoteOneTimePreKey;
    if (remoteKeyBundle.oneTimePreKey) {
      remoteOneTimePreKey = await CryptoUtils.importPublicKey(remoteKeyBundle.oneTimePreKey.publicKey, 'X25519');
    }

    // Generate ephemeral key pair for this session
    const ephemeralKeyPair = await CryptoUtils.generateKeyPair();

    // Perform Triple Diffie-Hellman (3DH)
    const dh1 = await CryptoUtils.deriveSharedSecret(this.identityKeyPair.privateKey, remoteSignedPreKey);
    const dh2 = await CryptoUtils.deriveSharedSecret(ephemeralKeyPair.privateKey, remoteIdentityKey);
    const dh3 = await CryptoUtils.deriveSharedSecret(ephemeralKeyPair.privateKey, remoteSignedPreKey);
    
    let dh4;
    if (remoteOneTimePreKey) {
      dh4 = await CryptoUtils.deriveSharedSecret(ephemeralKeyPair.privateKey, remoteOneTimePreKey);
    }

    // Combine shared secrets
    const sharedSecrets = [dh1, dh2, dh3];
    if (dh4) sharedSecrets.push(dh4);

    const combinedSecret = await CryptoUtils.combineSharedSecrets(sharedSecrets);
    
    // Derive root key
    const salt = CryptoUtils.generateRandomBytes(32);
    const rootKey = await CryptoUtils.deriveEncryptionKey(combinedSecret, salt, 'Root Key');

    const sessionState: SessionState = {
      rootKey: await this.exportKey(rootKey),
      messageNumberSend: 0,
      messageNumberReceive: 0,
      previousChainLength: 0,
      ratchetKeyPair: {
        private: await CryptoUtils.exportPrivateKey(ephemeralKeyPair.privateKey),
        public: await CryptoUtils.exportPublicKey(ephemeralKeyPair.publicKey),
      },
    };

    // Store session
    this.sessions.set(conversationId, sessionState);
    
    return sessionState;
  }

  /**
   * Encrypt message using session state
   */
  async encryptMessage(conversationId: string, content: string): Promise<EncryptedMessage> {
    const sessionState = this.sessions.get(conversationId);
    if (!sessionState) {
      throw new Error('Session not found for conversation');
    }

    // Derive message key from chain key or root key
    const chainKey = sessionState.chainKeySend || sessionState.rootKey;
    const messageKey = await this.deriveMessageKey(chainKey, sessionState.messageNumberSend);
    
    // Encrypt message
    const { encryptedData, iv } = await CryptoUtils.encryptMessage(content, messageKey);
    
    // Update session state
    sessionState.messageNumberSend++;
    this.sessions.set(conversationId, sessionState);
    
    return {
      encryptedContent: encryptedData,
      iv,
      messageNumber: sessionState.messageNumberSend - 1,
      ratchetKey: sessionState.ratchetKeyPair?.public || '',
      previousChainLength: sessionState.previousChainLength,
    };
  }

  /**
   * Decrypt message using session state
   */
  async decryptMessage(
    conversationId: string,
    encryptedMessage: EncryptedMessage
  ): Promise<string> {
    const sessionState = this.sessions.get(conversationId);
    if (!sessionState) {
      throw new Error('Session not found for conversation');
    }

    // Derive message key
    const chainKey = sessionState.chainKeyReceive || sessionState.rootKey;
    const messageKey = await this.deriveMessageKey(chainKey, encryptedMessage.messageNumber);
    
    // Decrypt message
    const decryptedContent = await CryptoUtils.decryptMessage(
      encryptedMessage.encryptedContent, 
      encryptedMessage.iv, 
      messageKey
    );
    
    // Update session state
    if (encryptedMessage.messageNumber >= sessionState.messageNumberReceive) {
      sessionState.messageNumberReceive = encryptedMessage.messageNumber + 1;
      this.sessions.set(conversationId, sessionState);
    }
    
    return decryptedContent;
  }

  /**
   * Get session state for a conversation
   */
  getSessionState(conversationId: string): SessionState | undefined {
    return this.sessions.get(conversationId);
  }

  /**
   * Update session state for a conversation
   */
  updateSessionState(conversationId: string, sessionState: SessionState): void {
    this.sessions.set(conversationId, sessionState);
  }

  /**
   * Generate new one-time pre-keys
   */
  async generateOneTimePreKeys(count: number, startId: number = 0): Promise<Array<{key_id: number, public_key: string}>> {
    const preKeys = [];

    for (let i = 0; i < count; i++) {
      const keyId = startId + i;
      const keyPair = await CryptoUtils.generateKeyPair();
      this.oneTimePreKeys.set(keyId, keyPair);

      preKeys.push({
        key_id: keyId,
        public_key: await CryptoUtils.exportPublicKey(keyPair.publicKey),
      });
    }

    return preKeys;
  }

  /**
   * Rotate signed pre-key
   */
  async rotateSignedPreKey(): Promise<{id: number, publicKey: string, signature: string}> {
    if (!this.signingKeyPair) {
      throw new Error('Signing key not initialized');
    }

    // Generate new signed pre-key
    this.signedPreKeyPair = await CryptoUtils.generateKeyPair();
    const signedPreKeyPublic = await CryptoUtils.exportPublicKey(this.signedPreKeyPair.publicKey);
    
    // Sign with identity signing key
    const signature = await CryptoUtils.signData(signedPreKeyPublic, this.signingKeyPair.privateKey);

    return {
      id: Date.now(), // Use timestamp as ID for simplicity
      publicKey: signedPreKeyPublic,
      signature: signature,
    };
  }

  // Helper methods
  private async deriveMessageKey(chainKey: string, messageNumber: number): Promise<CryptoKey> {
    const keyData = Uint8Array.from(atob(chainKey), c => c.charCodeAt(0));
    const salt = new TextEncoder().encode(`message_${messageNumber}`);
    
    return await CryptoUtils.deriveEncryptionKey(keyData.buffer, salt, 'Message Key');
  }

  private async exportKey(key: CryptoKey): Promise<string> {
    const exported = await window.crypto.subtle.exportKey('raw', key);
    return btoa(String.fromCharCode(...new Uint8Array(exported)));
  }

  /**
   * Clear all session data (for logout)
   */
  clearSessions(): void {
    this.sessions.clear();
  }

  /**
   * Export session state for storage
   */
  exportSessionState(conversationId: string): string | null {
    const sessionState = this.sessions.get(conversationId);
    return sessionState ? JSON.stringify(sessionState) : null;
  }

  /**
   * Import session state from storage
   */
  importSessionState(conversationId: string, sessionStateJson: string): void {
    try {
      const sessionState = JSON.parse(sessionStateJson) as SessionState;
      this.sessions.set(conversationId, sessionState);
    } catch (error) {
      console.error('Failed to import session state:', error);
    }
  }
}
