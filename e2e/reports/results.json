{"config": {"configFile": "/home/<USER>/chatapp/playwright.config.ts", "rootDir": "/home/<USER>/chatapp/e2e/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "e2e/reports/html"}], ["json", {"outputFile": "e2e/reports/results.json"}], ["junit", {"outputFile": "e2e/reports/results.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "firefox", "name": "firefox", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "webkit", "name": "webkit", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 6, "webServer": null}, "suites": [{"title": "messaging/encryption-session-fix-test.spec.ts", "file": "messaging/encryption-session-fix-test.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Encryption Session Fix Test", "file": "messaging/encryption-session-fix-test.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should initialize encryption sessions for existing conversations and send encrypted messages", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 19886, "error": {"message": "Error: locator.isVisible: Error: strict mode violation: locator('text=\"Encryption session test message 🔐\"') resolved to 2 elements:\n    1) <p class=\"text-sm text-gray-600 truncate mt-1\">…</p> aka getByRole('button', { name: '? Unknown User less than a' })\n    2) <div class=\"text-sm leading-relaxed\">Encryption session test message 🔐</div> aka getByTestId('message-list').getByText('Encryption session test')\n\nCall log:\n\u001b[2m    - checking visibility of locator('text=\"Encryption session test message 🔐\"')\u001b[22m\n", "stack": "Error: locator.isVisible: Error: strict mode violation: locator('text=\"Encryption session test message 🔐\"') resolved to 2 elements:\n    1) <p class=\"text-sm text-gray-600 truncate mt-1\">…</p> aka getByRole('button', { name: '? Unknown User less than a' })\n    2) <div class=\"text-sm leading-relaxed\">Encryption session test message 🔐</div> aka getByTestId('message-list').getByText('Encryption session test')\n\nCall log:\n\u001b[2m    - checking visibility of locator('text=\"Encryption session test message 🔐\"')\u001b[22m\n\n    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts:166:101\n    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts:162:18", "location": {"file": "/home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts", "column": 101, "line": 166}, "snippet": "\u001b[0m \u001b[90m 164 |\u001b[39m         \n \u001b[90m 165 |\u001b[39m         \u001b[90m// Check if the message appears in the UI\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 166 |\u001b[39m         \u001b[36mconst\u001b[39m messageVisible \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`text=\"${'Encryption session test message 🔐'}\"`\u001b[39m)\u001b[33m.\u001b[39misVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 167 |\u001b[39m         console\u001b[33m.\u001b[39mlog(\u001b[32m`💬 Test message visible: ${messageVisible}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 168 |\u001b[39m         \n \u001b[90m 169 |\u001b[39m         \u001b[90m// Check for any messages\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts", "column": 101, "line": 166}, "message": "Error: locator.isVisible: Error: strict mode violation: locator('text=\"Encryption session test message 🔐\"') resolved to 2 elements:\n    1) <p class=\"text-sm text-gray-600 truncate mt-1\">…</p> aka getByRole('button', { name: '? Unknown User less than a' })\n    2) <div class=\"text-sm leading-relaxed\">Encryption session test message 🔐</div> aka getByTestId('message-list').getByText('Encryption session test')\n\nCall log:\n\u001b[2m    - checking visibility of locator('text=\"Encryption session test message 🔐\"')\u001b[22m\n\n\n\u001b[0m \u001b[90m 164 |\u001b[39m         \n \u001b[90m 165 |\u001b[39m         \u001b[90m// Check if the message appears in the UI\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 166 |\u001b[39m         \u001b[36mconst\u001b[39m messageVisible \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`text=\"${'Encryption session test message 🔐'}\"`\u001b[39m)\u001b[33m.\u001b[39misVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 167 |\u001b[39m         console\u001b[33m.\u001b[39mlog(\u001b[32m`💬 Test message visible: ${messageVisible}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 168 |\u001b[39m         \n \u001b[90m 169 |\u001b[39m         \u001b[90m// Check for any messages\u001b[39m\u001b[0m\n\u001b[2m    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts:166:101\u001b[22m\n\u001b[2m    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts:162:18\u001b[22m"}], "stdout": [{"text": "🔍 Starting encryption session fix test...\n"}, {"text": "✅ User authenticated\n"}, {"text": "🔍 Looking for existing conversations...\n"}, {"text": "📋 Found 0 existing conversations\n"}, {"text": "🔍 No existing conversations, creating one...\n"}, {"text": "✅ Created new conversation\n"}, {"text": "🔍 Testing encryption session initialization...\n"}, {"text": "📤 Sending message: Encryption session test message 🔐\n"}, {"text": "✅ Message sent successfully\n"}, {"text": "🔍 Analyzing encryption logs...\n"}, {"text": "🔐 Encryption logs:\n"}, {"text": "  1. 🚀 [SEND_MESSAGE] Starting message send process {conversationId: draft-46f2c6db-d796-4713-9415-0eb57bb496d4-1754491804255, contentLength: 34, messageType: TEXT, socketConnected: true, userAvailable: true}\n"}, {"text": "  2. ⚠️ [SEND_MESSAGE] Encryption not initialized - will send unencrypted message\n"}, {"text": "  3. 🔄 [SEND_MESSAGE] Generated tempId: 45fe1114-e85d-4c12-8e84-f0d63d668fc5\n"}, {"text": "  4. 📝 [SEND_MESSAGE] Processing draft conversation: draft-46f2c6db-d796-4713-9415-0eb57bb496d4-1754491804255\n"}, {"text": "  5. 👤 [SEND_MESSAGE] Extracted userId from draft: 46f2c6db-d796-4713-9415-0eb57bb496d4\n"}, {"text": "  6. 🔄 [SEND_MESSAGE] Creating real conversation via socket...\n"}, {"text": "  7. ✅ [SEND_MESSAGE] Conversation creation result: {id: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375, type: DIRECT, name: null, createdAt: 2025-08-04T04:56:02.038Z, updatedAt: 2025-08-06T14:38:48.275Z}\n"}, {"text": "  8. 🎯 [SEND_MESSAGE] Using real conversation ID: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375\n"}, {"text": "  9. 🔄 [SEND_MESSAGE] Converted draft to real conversation in Redux\n"}, {"text": "  10. 🔐 [SEND_MESSAGE] Initializing encryption session for new conversation...\n"}, {"text": "  11. 🔐 Initializing session for conversation: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375\n"}, {"text": "  12. 🔐 ❌ API error during session initialization: SyntaxError: Cannot create a key using the specified key usages.\n"}, {"text": "  13. 🔐 ❌ Failed to initialize session: SyntaxError: Cannot create a key using the specified key usages.\n"}, {"text": "  14. 🔐 [SEND_MESSAGE] ❌ Failed to initialize encryption session: SyntaxError: Cannot create a key using the specified key usages.\n"}, {"text": "  15. 🟡 [SEND_MESSAGE] Skipping Redux store for real conversation\n"}, {"text": "  16. 🟡 [SEND_MESSAGE] Adding optimistic message to RTK Query cache...\n"}, {"text": "  17. 🔶 [RTK_CACHE] Content: Encryption session test message 🔐\n"}, {"text": "  18. 🟡 [SEND_MESSAGE] ✅ Added optimistic message to RTK Query cache\n"}, {"text": "  19. 🟡 [SEND_MESSAGE] ✅ Optimistic message setup complete\n"}, {"text": "  20. 🟡 [SEND_MESSAGE] TempId: 45fe1114-e85d-4c12-8e84-f0d63d668fc5\n"}, {"text": "  21. 🟡 [SEND_MESSAGE] Conversation: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375\n"}, {"text": "  22. 🔐 [SEND_MESSAGE] Encrypting message...\n"}, {"text": "  23. 🔐 Encrypting message for conversation: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375\n"}, {"text": "  24. 🔐 ❌ Failed to encrypt message: Error: Session not found for conversation\n    at SignalProtocol.encryptMessage (http://localhost:5000/src/utils/signalProtocol.ts:94:13)\n    at http://localhost:5000/src/contexts/EncryptionContext.tsx:139:64\n    at http://localhost:5000/src/contexts/SocketContext.tsx?t=1754491604585:461:38\n    at async handleSubmit (http://localhost:5000/src/components/Chat/MessageInput.tsx?t=1754491604585:60:7)\n"}, {"text": "  25. 🔐 [SEND_MESSAGE] ❌ Failed to encrypt message: Error: Session not found for conversation\n    at SignalProtocol.encryptMessage (http://localhost:5000/src/utils/signalProtocol.ts:94:13)\n    at http://localhost:5000/src/contexts/EncryptionContext.tsx:139:64\n    at http://localhost:5000/src/contexts/SocketContext.tsx?t=1754491604585:461:38\n    at async handleSubmit (http://localhost:5000/src/components/Chat/MessageInput.tsx?t=1754491604585:60:7)\n"}, {"text": "  26. 🔐 [SEND_MESSAGE] Error details: {message: Session not found for conversation, conversationId: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375, contentLength: 34}\n"}, {"text": "  27. 📡 [SEND_MESSAGE] Falling back to unencrypted message...\n"}, {"text": "  28. 📡 [SEND_MESSAGE] ✅ Unencrypted message emitted to socket\n"}, {"text": "  29. 🔵 [NEW_MESSAGE] Received new_message event: {id: cfc568d6-1371-4b7b-98e6-4c804524fdef, conversationId: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375, sender: Object, content: Encryption session test message 🔐, messageType: TEXT}\n"}, {"text": "  30. 🔵 [NEW_MESSAGE] Content: Encryption session test message 🔐\n"}, {"text": "  31. 🔶 [RTK_CACHE] Content: Encryption session test message 🔐\n"}, {"text": "  32. 🔶 [RTK_CACHE] Searching for content: Encryption session test message 🔐\n"}, {"text": "  33. 🔶 [RTK_CACHE] Cache[16]: ID=db4de890-dd94-4414-a34d-8774181f9d79, content=\"Encryption fix test message 🔐\", isOptimistic=undefined, sender=78bb8aa7-b130-41bb-bb02-123ad01432bc\n"}, {"text": "📊 Encryption Analysis:\n"}, {"text": "  - Session initialization attempted: true\n"}, {"text": "  - Session initialized: false\n"}, {"text": "  - Encryption attempted: true\n"}, {"text": "  - Encryption succeeded: false\n"}, {"text": "  - Encryption failed: true\n"}, {"text": "⚠️ INFO: Message fell back to unencrypted (expected for test environment)\n"}, {"text": "✅ FIX WORKING: Encryption session initialization is being attempted for existing conversations\n"}, {"text": "🔍 Checking message display...\n"}, {"text": "🔐 Encryption logs at failure:\n"}, {"text": "  1. 🚀 [SEND_MESSAGE] Starting message send process {conversationId: draft-46f2c6db-d796-4713-9415-0eb57bb496d4-1754491804255, contentLength: 34, messageType: TEXT, socketConnected: true, userAvailable: true}\n"}, {"text": "  2. ⚠️ [SEND_MESSAGE] Encryption not initialized - will send unencrypted message\n"}, {"text": "  3. 🔄 [SEND_MESSAGE] Generated tempId: 45fe1114-e85d-4c12-8e84-f0d63d668fc5\n"}, {"text": "  4. 📝 [SEND_MESSAGE] Processing draft conversation: draft-46f2c6db-d796-4713-9415-0eb57bb496d4-1754491804255\n"}, {"text": "  5. 👤 [SEND_MESSAGE] Extracted userId from draft: 46f2c6db-d796-4713-9415-0eb57bb496d4\n"}, {"text": "  6. 🔄 [SEND_MESSAGE] Creating real conversation via socket...\n"}, {"text": "  7. ✅ [SEND_MESSAGE] Conversation creation result: {id: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375, type: DIRECT, name: null, createdAt: 2025-08-04T04:56:02.038Z, updatedAt: 2025-08-06T14:38:48.275Z}\n"}, {"text": "  8. 🎯 [SEND_MESSAGE] Using real conversation ID: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375\n"}, {"text": "  9. 🔄 [SEND_MESSAGE] Converted draft to real conversation in Redux\n"}, {"text": "  10. 🔐 [SEND_MESSAGE] Initializing encryption session for new conversation...\n"}, {"text": "  11. 🔐 Initializing session for conversation: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375\n"}, {"text": "  12. 🔐 ❌ API error during session initialization: SyntaxError: Cannot create a key using the specified key usages.\n"}, {"text": "  13. 🔐 ❌ Failed to initialize session: SyntaxError: Cannot create a key using the specified key usages.\n"}, {"text": "  14. 🔐 [SEND_MESSAGE] ❌ Failed to initialize encryption session: SyntaxError: Cannot create a key using the specified key usages.\n"}, {"text": "  15. 🟡 [SEND_MESSAGE] Skipping Redux store for real conversation\n"}, {"text": "  16. 🟡 [SEND_MESSAGE] Adding optimistic message to RTK Query cache...\n"}, {"text": "  17. 🔶 [RTK_CACHE] Content: Encryption session test message 🔐\n"}, {"text": "  18. 🟡 [SEND_MESSAGE] ✅ Added optimistic message to RTK Query cache\n"}, {"text": "  19. 🟡 [SEND_MESSAGE] ✅ Optimistic message setup complete\n"}, {"text": "  20. 🟡 [SEND_MESSAGE] TempId: 45fe1114-e85d-4c12-8e84-f0d63d668fc5\n"}, {"text": "  21. 🟡 [SEND_MESSAGE] Conversation: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375\n"}, {"text": "  22. 🔐 [SEND_MESSAGE] Encrypting message...\n"}, {"text": "  23. 🔐 Encrypting message for conversation: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375\n"}, {"text": "  24. 🔐 ❌ Failed to encrypt message: Error: Session not found for conversation\n    at SignalProtocol.encryptMessage (http://localhost:5000/src/utils/signalProtocol.ts:94:13)\n    at http://localhost:5000/src/contexts/EncryptionContext.tsx:139:64\n    at http://localhost:5000/src/contexts/SocketContext.tsx?t=1754491604585:461:38\n    at async handleSubmit (http://localhost:5000/src/components/Chat/MessageInput.tsx?t=1754491604585:60:7)\n"}, {"text": "  25. 🔐 [SEND_MESSAGE] ❌ Failed to encrypt message: Error: Session not found for conversation\n    at SignalProtocol.encryptMessage (http://localhost:5000/src/utils/signalProtocol.ts:94:13)\n    at http://localhost:5000/src/contexts/EncryptionContext.tsx:139:64\n    at http://localhost:5000/src/contexts/SocketContext.tsx?t=1754491604585:461:38\n    at async handleSubmit (http://localhost:5000/src/components/Chat/MessageInput.tsx?t=1754491604585:60:7)\n"}, {"text": "  26. 🔐 [SEND_MESSAGE] Error details: {message: Session not found for conversation, conversationId: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375, contentLength: 34}\n"}, {"text": "  27. 📡 [SEND_MESSAGE] Falling back to unencrypted message...\n"}, {"text": "  28. 📡 [SEND_MESSAGE] ✅ Unencrypted message emitted to socket\n"}, {"text": "  29. 🔵 [NEW_MESSAGE] Received new_message event: {id: cfc568d6-1371-4b7b-98e6-4c804524fdef, conversationId: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375, sender: Object, content: Encryption session test message 🔐, messageType: TEXT}\n"}, {"text": "  30. 🔵 [NEW_MESSAGE] Content: Encryption session test message 🔐\n"}, {"text": "  31. 🔶 [RTK_CACHE] Content: Encryption session test message 🔐\n"}, {"text": "  32. 🔶 [RTK_CACHE] Searching for content: Encryption session test message 🔐\n"}, {"text": "  33. 🔶 [RTK_CACHE] Cache[16]: ID=db4de890-dd94-4414-a34d-8774181f9d79, content=\"Encryption fix test message 🔐\", isOptimistic=undefined, sender=78bb8aa7-b130-41bb-bb02-123ad01432bc\n"}], "stderr": [{"text": "❌ Test failed: locator.isVisible: Error: strict mode violation: locator('text=\"Encryption session test message 🔐\"') resolved to 2 elements:\n    1) <p class=\"text-sm text-gray-600 truncate mt-1\">…</p> aka getByRole('button', { name: '? Unknown User less than a' })\n    2) <div class=\"text-sm leading-relaxed\">Encryption session test message 🔐</div> aka getByTestId('message-list').getByText('Encryption session test')\n\nCall log:\n\u001b[2m    - checking visibility of locator('text=\"Encryption session test message 🔐\"')\u001b[22m\n\n    at \u001b[90m/home/<USER>/chatapp/\u001b[39me2e/tests/messaging/encryption-session-fix-test.spec.ts:166:101\n    at \u001b[90m/home/<USER>/chatapp/\u001b[39me2e/tests/messaging/encryption-session-fix-test.spec.ts:162:18 {\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    stepId: \u001b[32m'pw:api@35'\u001b[39m,\n    location: {\n      file: \u001b[32m'/home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts'\u001b[39m,\n      line: \u001b[33m166\u001b[39m,\n      column: \u001b[33m101\u001b[39m\n    },\n    category: \u001b[32m'pw:api'\u001b[39m,\n    title: \u001b[32m`Is visible locator('text=\"Encryption session test message 🔐\"')`\u001b[39m,\n    apiName: \u001b[32m'locator.isVisible'\u001b[39m,\n    params: {\n      selector: \u001b[32m'text=\"Encryption session test message 🔐\"'\u001b[39m,\n      strict: \u001b[33mtrue\u001b[39m\n    },\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'pw:api@35'\u001b[39m,\n      skip: \u001b[36m[Function (anonymous)]\u001b[39m\n    },\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1754491812400\u001b[39m,\n    error: {\n      message: \u001b[32m`Error: locator.isVisible: Error: strict mode violation: locator('text=\"Encryption session test message 🔐\"') resolved to 2 elements:\\n`\u001b[39m +\n        \u001b[32m`    1) <p class=\"text-sm text-gray-600 truncate mt-1\">…</p> aka getByRole('button', { name: '? Unknown User less than a' })\\n`\u001b[39m +\n        \u001b[32m`    2) <div class=\"text-sm leading-relaxed\">Encryption session test message 🔐</div> aka getByTestId('message-list').getByText('Encryption session test')\\n`\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m`\\x1B[2m    - checking visibility of locator('text=\"Encryption session test message 🔐\"')\\x1B[22m\\n`\u001b[39m,\n      stack: \u001b[32m`Error: locator.isVisible: Error: strict mode violation: locator('text=\"Encryption session test message 🔐\"') resolved to 2 elements:\\n`\u001b[39m +\n        \u001b[32m`    1) <p class=\"text-sm text-gray-600 truncate mt-1\">…</p> aka getByRole('button', { name: '? Unknown User less than a' })\\n`\u001b[39m +\n        \u001b[32m`    2) <div class=\"text-sm leading-relaxed\">Encryption session test message 🔐</div> aka getByTestId('message-list').getByText('Encryption session test')\\n`\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m`\\x1B[2m    - checking visibility of locator('text=\"Encryption session test message 🔐\"')\\x1B[22m\\n`\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts:166:101\\n'\u001b[39m +\n        \u001b[32m'    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts:162:18'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}], "retry": 0, "steps": [{"title": "Login and setup", "duration": 3824}, {"title": "Select existing conversation", "duration": 12072}, {"title": "Send message with encryption session initialization", "duration": 5099}, {"title": "Analyze encryption session behavior", "duration": 1}, {"title": "Verify message display", "duration": 27, "error": {"message": "Error: locator.isVisible: Error: strict mode violation: locator('text=\"Encryption session test message 🔐\"') resolved to 2 elements:\n    1) <p class=\"text-sm text-gray-600 truncate mt-1\">…</p> aka getByRole('button', { name: '? Unknown User less than a' })\n    2) <div class=\"text-sm leading-relaxed\">Encryption session test message 🔐</div> aka getByTestId('message-list').getByText('Encryption session test')\n\nCall log:\n\u001b[2m    - checking visibility of locator('text=\"Encryption session test message 🔐\"')\u001b[22m\n", "stack": "Error: locator.isVisible: Error: strict mode violation: locator('text=\"Encryption session test message 🔐\"') resolved to 2 elements:\n    1) <p class=\"text-sm text-gray-600 truncate mt-1\">…</p> aka getByRole('button', { name: '? Unknown User less than a' })\n    2) <div class=\"text-sm leading-relaxed\">Encryption session test message 🔐</div> aka getByTestId('message-list').getByText('Encryption session test')\n\nCall log:\n\u001b[2m    - checking visibility of locator('text=\"Encryption session test message 🔐\"')\u001b[22m\n\n    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts:166:101\n    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts:162:18", "location": {"file": "/home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts", "column": 101, "line": 166}, "snippet": "\u001b[0m \u001b[90m 164 |\u001b[39m         \n \u001b[90m 165 |\u001b[39m         \u001b[90m// Check if the message appears in the UI\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 166 |\u001b[39m         \u001b[36mconst\u001b[39m messageVisible \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m`text=\"${'Encryption session test message 🔐'}\"`\u001b[39m)\u001b[33m.\u001b[39misVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 167 |\u001b[39m         console\u001b[33m.\u001b[39mlog(\u001b[32m`💬 Test message visible: ${messageVisible}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 168 |\u001b[39m         \n \u001b[90m 169 |\u001b[39m         \u001b[90m// Check for any messages\u001b[39m\u001b[0m"}}], "startTime": "2025-08-06T14:49:48.252Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/home/<USER>/chatapp/e2e/test-results/messaging-encryption-sessi-1ce68-and-send-encrypted-messages-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/home/<USER>/chatapp/e2e/test-results/messaging-encryption-sessi-1ce68-and-send-encrypted-messages-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/home/<USER>/chatapp/e2e/test-results/messaging-encryption-sessi-1ce68-and-send-encrypted-messages-chromium/error-context.md"}], "errorLocation": {"file": "/home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts", "column": 101, "line": 166}}], "status": "unexpected"}], "id": "00b0ee6f6b7256078145-83b76f885f1927425466", "file": "messaging/encryption-session-fix-test.spec.ts", "line": 4, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-08-06T14:49:47.150Z", "duration": 22881.377, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}