{"config": {"configFile": "/home/<USER>/chatapp/playwright.config.ts", "rootDir": "/home/<USER>/chatapp/e2e/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "e2e/reports/html"}], ["json", {"outputFile": "e2e/reports/results.json"}], ["junit", {"outputFile": "e2e/reports/results.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "firefox", "name": "firefox", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "webkit", "name": "webkit", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/home/<USER>/chatapp/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "/home/<USER>/chatapp/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 6, "webServer": null}, "suites": [{"title": "messaging/encryption-database-issues-reproduction.spec.ts", "file": "messaging/encryption-database-issues-reproduction.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Encryption and Database Issues Reproduction", "file": "messaging/encryption-database-issues-reproduction.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should reproduce encryption session failures and database schema errors", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "timedOut", "duration": 40027, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/home/<USER>/chatapp/e2e/tests/messaging/encryption-database-issues-reproduction.spec.ts", "column": 20, "line": 43}, "message": "TimeoutError: page.goto: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5000/login\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 41 |\u001b[39m       \u001b[90m// Step 1: Login and setup\u001b[39m\n \u001b[90m 42 |\u001b[39m       \u001b[36mawait\u001b[39m test\u001b[33m.\u001b[39mstep(\u001b[32m'Login and setup'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 43 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 44 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 45 |\u001b[39m         \n \u001b[90m 46 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder*=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-database-issues-reproduction.spec.ts:43:20\u001b[22m\n\u001b[2m    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-database-issues-reproduction.spec.ts:42:18\u001b[22m"}], "stdout": [{"text": "🧪 Starting encryption and database issues reproduction test...\n"}, {"text": "\n📋 Final Log Summary:\n"}, {"text": "Total logs captured: 0\n"}, {"text": "Error logs: 0\n"}, {"text": "Encryption logs: 0\n"}, {"text": "Database logs: 0\n"}], "stderr": [{"text": "❌ Test execution failed: page.goto: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5000/login\", waiting until \"load\"\u001b[22m\n\n    at \u001b[90m/home/<USER>/chatapp/\u001b[39me2e/tests/messaging/encryption-database-issues-reproduction.spec.ts:43:20\n    at \u001b[90m/home/<USER>/chatapp/\u001b[39me2e/tests/messaging/encryption-database-issues-reproduction.spec.ts:42:18 {\n  name: \u001b[32m'TimeoutError'\u001b[39m,\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    stepId: \u001b[32m'pw:api@9'\u001b[39m,\n    location: {\n      file: \u001b[32m'/home/<USER>/chatapp/e2e/tests/messaging/encryption-database-issues-reproduction.spec.ts'\u001b[39m,\n      line: \u001b[33m43\u001b[39m,\n      column: \u001b[33m20\u001b[39m\n    },\n    category: \u001b[32m'pw:api'\u001b[39m,\n    title: \u001b[32m'Navigate to \"/login\"'\u001b[39m,\n    apiName: \u001b[32m'page.goto'\u001b[39m,\n    params: { url: \u001b[32m'/login'\u001b[39m, waitUntil: \u001b[32m'load'\u001b[39m, timeout: \u001b[33m30000\u001b[39m },\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'pw:api@9'\u001b[39m,\n      skip: \u001b[36m[Function (anonymous)]\u001b[39m\n    },\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1754629602509\u001b[39m,\n    error: {\n      message: \u001b[32m'TimeoutError: page.goto: Timeout 30000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - navigating to \"http://localhost:5000/login\", waiting until \"load\"\\x1B[22m\\n'\u001b[39m,\n      stack: \u001b[32m'TimeoutError: page.goto: Timeout 30000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m'\\x1B[2m  - navigating to \"http://localhost:5000/login\", waiting until \"load\"\\x1B[22m\\n'\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-database-issues-reproduction.spec.ts:43:20\\n'\u001b[39m +\n        \u001b[32m'    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-database-issues-reproduction.spec.ts:42:18'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}], "retry": 0, "steps": [{"title": "Login and setup", "duration": 30002, "error": {"message": "TimeoutError: page.goto: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5000/login\", waiting until \"load\"\u001b[22m\n", "stack": "TimeoutError: page.goto: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5000/login\", waiting until \"load\"\u001b[22m\n\n    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-database-issues-reproduction.spec.ts:43:20\n    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-database-issues-reproduction.spec.ts:42:18", "location": {"file": "/home/<USER>/chatapp/e2e/tests/messaging/encryption-database-issues-reproduction.spec.ts", "column": 20, "line": 43}, "snippet": "\u001b[0m \u001b[90m 41 |\u001b[39m       \u001b[90m// Step 1: Login and setup\u001b[39m\n \u001b[90m 42 |\u001b[39m       \u001b[36mawait\u001b[39m test\u001b[33m.\u001b[39mstep(\u001b[32m'Login and setup'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 43 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 44 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 45 |\u001b[39m         \n \u001b[90m 46 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[placeholder*=\"email\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}}], "startTime": "2025-08-08T05:06:11.676Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "5bd82ba1e58d3d40d39a-334f2a0fcffc376d36e7", "file": "messaging/encryption-database-issues-reproduction.spec.ts", "line": 4, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-08-08T05:06:11.021Z", "duration": 41382.917, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}