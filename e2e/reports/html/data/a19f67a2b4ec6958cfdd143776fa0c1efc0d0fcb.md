# Page snapshot

```yaml
- heading "Chat App" [level=1]
- text: Connected Welcome, Test User1
- button "Logout"
- heading "Conversations" [level=2]
- button "New Chat"
- 'button "? Unknown User less than a minute ago testuser1: Encryption session test message 🔐"':
  - text: "?"
  - heading "Unknown User" [level=3]
  - text: less than a minute ago
  - paragraph: "testuser1: Encryption session test message 🔐"
- 'button "? Unknown User 9 minutes ago harry: 1"':
  - text: "?"
  - heading "Unknown User" [level=3]
  - text: 9 minutes ago
  - paragraph: "harry: 1"
- text: Redux state debug test message 🔍 2 days ago Dashboard Redux debug test message 🔍 2 days ago MessageList debug test message 🔍 2 days ago Dashboard render debug test message 🔍 2 days ago MessageList final debug test message 🔍 2 days ago MessageList final debug test message 🔍 2 days ago MessageList final debug test message 🔍 2 days ago API debug test message 🔍 2 days ago Network debug test message 🔍 2 days ago API response debug test message 🔍 2 days ago Second message for timing test 🕐 2 days ago MessageList final debug test message 🔍 2 days ago Transform fix test message 🔧 2 days ago Test message for network resilience 2 days ago Hello User2! This is a test message from the E2E test 🚀 2 days ago Transform fix test message 🔧 2 days ago Encryption fix test message 🔐 about 1 hour ago Test message 1 🚀 about 1 hour ago Test message 2 ⚡ about 1 hour ago Test message 3 🎯 about 1 hour ago Encryption session test message 🔐 less than a minute ago
- button
- textbox "Type a message..."
- button "Send" [disabled]
```