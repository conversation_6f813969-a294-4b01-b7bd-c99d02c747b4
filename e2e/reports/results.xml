<testsuites id="" name="" tests="1" failures="1" skipped="0" errors="0" time="41.382917">
<testsuite name="messaging/encryption-database-issues-reproduction.spec.ts" timestamp="2025-08-08T05:06:11.101Z" hostname="chromium" tests="1" failures="1" skipped="0" time="40.027" errors="0">
<testcase name="Encryption and Database Issues Reproduction › should reproduce encryption session failures and database schema errors" classname="messaging/encryption-database-issues-reproduction.spec.ts" time="40.027">
<failure message="encryption-database-issues-reproduction.spec.ts:4:7 should reproduce encryption session failures and database schema errors" type="FAILURE">
<![CDATA[  [chromium] › messaging/encryption-database-issues-reproduction.spec.ts:4:7 › Encryption and Database Issues Reproduction › should reproduce encryption session failures and database schema errors › Login and setup 

    Test timeout of 30000ms exceeded.

    TimeoutError: page.goto: Timeout 30000ms exceeded.
    Call log:
      - navigating to "http://localhost:5000/login", waiting until "load"


      41 |       // Step 1: Login and setup
      42 |       await test.step('Login and setup', async () => {
    > 43 |         await page.goto('/login');
         |                    ^
      44 |         await page.waitForLoadState('networkidle');
      45 |         
      46 |         await page.fill('input[placeholder*="email"]', '<EMAIL>');
        at /home/<USER>/chatapp/e2e/tests/messaging/encryption-database-issues-reproduction.spec.ts:43:20
        at /home/<USER>/chatapp/e2e/tests/messaging/encryption-database-issues-reproduction.spec.ts:42:18
]]>
</failure>
<system-out>
<![CDATA[🧪 Starting encryption and database issues reproduction test...

📋 Final Log Summary:
Total logs captured: 0
Error logs: 0
Encryption logs: 0
Database logs: 0
]]>
</system-out>
<system-err>
<![CDATA[❌ Test execution failed: page.goto: Timeout 30000ms exceeded.
Call log:
[2m  - navigating to "http://localhost:5000/login", waiting until "load"[22m

    at [90m/home/<USER>/chatapp/[39me2e/tests/messaging/encryption-database-issues-reproduction.spec.ts:43:20
    at [90m/home/<USER>/chatapp/[39me2e/tests/messaging/encryption-database-issues-reproduction.spec.ts:42:18 {
  name: [32m'TimeoutError'[39m,
  [[32mSymbol(step)[39m]: {
    stepId: [32m'pw:api@9'[39m,
    location: {
      file: [32m'/home/<USER>/chatapp/e2e/tests/messaging/encryption-database-issues-reproduction.spec.ts'[39m,
      line: [33m43[39m,
      column: [33m20[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m'Navigate to "/login"'[39m,
    apiName: [32m'page.goto'[39m,
    params: { url: [32m'/login'[39m, waitUntil: [32m'load'[39m, timeout: [33m30000[39m },
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@9'[39m,
      skip: [36m[Function (anonymous)][39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1754629602509[39m,
    error: {
      message: [32m'TimeoutError: page.goto: Timeout 30000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m'\x1B[2m  - navigating to "http://localhost:5000/login", waiting until "load"\x1B[22m\n'[39m,
      stack: [32m'TimeoutError: page.goto: Timeout 30000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m'\x1B[2m  - navigating to "http://localhost:5000/login", waiting until "load"\x1B[22m\n'[39m +
        [32m'\n'[39m +
        [32m'    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-database-issues-reproduction.spec.ts:43:20\n'[39m +
        [32m'    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-database-issues-reproduction.spec.ts:42:18'[39m,
      cause: [90mundefined[39m
    }
  }
}
]]>
</system-err>
</testcase>
</testsuite>
</testsuites>