<testsuites id="" name="" tests="1" failures="1" skipped="0" errors="0" time="22.881377">
<testsuite name="messaging/encryption-session-fix-test.spec.ts" timestamp="2025-08-06T14:49:47.408Z" hostname="chromium" tests="1" failures="1" skipped="0" time="19.886" errors="0">
<testcase name="Encryption Session Fix Test › should initialize encryption sessions for existing conversations and send encrypted messages" classname="messaging/encryption-session-fix-test.spec.ts" time="19.886">
<failure message="encryption-session-fix-test.spec.ts:4:7 should initialize encryption sessions for existing conversations and send encrypted messages" type="FAILURE">
<![CDATA[  [chromium] › messaging/encryption-session-fix-test.spec.ts:4:7 › Encryption Session Fix Test › should initialize encryption sessions for existing conversations and send encrypted messages › Verify message display 

    Error: locator.isVisible: Error: strict mode violation: locator('text="Encryption session test message 🔐"') resolved to 2 elements:
        1) <p class="text-sm text-gray-600 truncate mt-1">…</p> aka getByRole('button', { name: '? Unknown User less than a' })
        2) <div class="text-sm leading-relaxed">Encryption session test message 🔐</div> aka getByTestId('message-list').getByText('Encryption session test')

    Call log:
        - checking visibility of locator('text="Encryption session test message 🔐"')


      164 |         
      165 |         // Check if the message appears in the UI
    > 166 |         const messageVisible = await page.locator(`text="${'Encryption session test message 🔐'}"`).isVisible();
          |                                                                                                     ^
      167 |         console.log(`💬 Test message visible: ${messageVisible}`);
      168 |         
      169 |         // Check for any messages
        at /home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts:166:101
        at /home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts:162:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/messaging-encryption-sessi-1ce68-and-send-encrypted-messages-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ../test-results/messaging-encryption-sessi-1ce68-and-send-encrypted-messages-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/messaging-encryption-sessi-1ce68-and-send-encrypted-messages-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔍 Starting encryption session fix test...
✅ User authenticated
🔍 Looking for existing conversations...
📋 Found 0 existing conversations
🔍 No existing conversations, creating one...
✅ Created new conversation
🔍 Testing encryption session initialization...
📤 Sending message: Encryption session test message 🔐
✅ Message sent successfully
🔍 Analyzing encryption logs...
🔐 Encryption logs:
  1. 🚀 [SEND_MESSAGE] Starting message send process {conversationId: draft-46f2c6db-d796-4713-9415-0eb57bb496d4-1754491804255, contentLength: 34, messageType: TEXT, socketConnected: true, userAvailable: true}
  2. ⚠️ [SEND_MESSAGE] Encryption not initialized - will send unencrypted message
  3. 🔄 [SEND_MESSAGE] Generated tempId: 45fe1114-e85d-4c12-8e84-f0d63d668fc5
  4. 📝 [SEND_MESSAGE] Processing draft conversation: draft-46f2c6db-d796-4713-9415-0eb57bb496d4-1754491804255
  5. 👤 [SEND_MESSAGE] Extracted userId from draft: 46f2c6db-d796-4713-9415-0eb57bb496d4
  6. 🔄 [SEND_MESSAGE] Creating real conversation via socket...
  7. ✅ [SEND_MESSAGE] Conversation creation result: {id: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375, type: DIRECT, name: null, createdAt: 2025-08-04T04:56:02.038Z, updatedAt: 2025-08-06T14:38:48.275Z}
  8. 🎯 [SEND_MESSAGE] Using real conversation ID: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375
  9. 🔄 [SEND_MESSAGE] Converted draft to real conversation in Redux
  10. 🔐 [SEND_MESSAGE] Initializing encryption session for new conversation...
  11. 🔐 Initializing session for conversation: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375
  12. 🔐 ❌ API error during session initialization: SyntaxError: Cannot create a key using the specified key usages.
  13. 🔐 ❌ Failed to initialize session: SyntaxError: Cannot create a key using the specified key usages.
  14. 🔐 [SEND_MESSAGE] ❌ Failed to initialize encryption session: SyntaxError: Cannot create a key using the specified key usages.
  15. 🟡 [SEND_MESSAGE] Skipping Redux store for real conversation
  16. 🟡 [SEND_MESSAGE] Adding optimistic message to RTK Query cache...
  17. 🔶 [RTK_CACHE] Content: Encryption session test message 🔐
  18. 🟡 [SEND_MESSAGE] ✅ Added optimistic message to RTK Query cache
  19. 🟡 [SEND_MESSAGE] ✅ Optimistic message setup complete
  20. 🟡 [SEND_MESSAGE] TempId: 45fe1114-e85d-4c12-8e84-f0d63d668fc5
  21. 🟡 [SEND_MESSAGE] Conversation: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375
  22. 🔐 [SEND_MESSAGE] Encrypting message...
  23. 🔐 Encrypting message for conversation: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375
  24. 🔐 ❌ Failed to encrypt message: Error: Session not found for conversation
    at SignalProtocol.encryptMessage (http://localhost:5000/src/utils/signalProtocol.ts:94:13)
    at http://localhost:5000/src/contexts/EncryptionContext.tsx:139:64
    at http://localhost:5000/src/contexts/SocketContext.tsx?t=1754491604585:461:38
    at async handleSubmit (http://localhost:5000/src/components/Chat/MessageInput.tsx?t=1754491604585:60:7)
  25. 🔐 [SEND_MESSAGE] ❌ Failed to encrypt message: Error: Session not found for conversation
    at SignalProtocol.encryptMessage (http://localhost:5000/src/utils/signalProtocol.ts:94:13)
    at http://localhost:5000/src/contexts/EncryptionContext.tsx:139:64
    at http://localhost:5000/src/contexts/SocketContext.tsx?t=1754491604585:461:38
    at async handleSubmit (http://localhost:5000/src/components/Chat/MessageInput.tsx?t=1754491604585:60:7)
  26. 🔐 [SEND_MESSAGE] Error details: {message: Session not found for conversation, conversationId: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375, contentLength: 34}
  27. 📡 [SEND_MESSAGE] Falling back to unencrypted message...
  28. 📡 [SEND_MESSAGE] ✅ Unencrypted message emitted to socket
  29. 🔵 [NEW_MESSAGE] Received new_message event: {id: cfc568d6-1371-4b7b-98e6-4c804524fdef, conversationId: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375, sender: Object, content: Encryption session test message 🔐, messageType: TEXT}
  30. 🔵 [NEW_MESSAGE] Content: Encryption session test message 🔐
  31. 🔶 [RTK_CACHE] Content: Encryption session test message 🔐
  32. 🔶 [RTK_CACHE] Searching for content: Encryption session test message 🔐
  33. 🔶 [RTK_CACHE] Cache[16]: ID=db4de890-dd94-4414-a34d-8774181f9d79, content="Encryption fix test message 🔐", isOptimistic=undefined, sender=78bb8aa7-b130-41bb-bb02-123ad01432bc
📊 Encryption Analysis:
  - Session initialization attempted: true
  - Session initialized: false
  - Encryption attempted: true
  - Encryption succeeded: false
  - Encryption failed: true
⚠️ INFO: Message fell back to unencrypted (expected for test environment)
✅ FIX WORKING: Encryption session initialization is being attempted for existing conversations
🔍 Checking message display...
🔐 Encryption logs at failure:
  1. 🚀 [SEND_MESSAGE] Starting message send process {conversationId: draft-46f2c6db-d796-4713-9415-0eb57bb496d4-1754491804255, contentLength: 34, messageType: TEXT, socketConnected: true, userAvailable: true}
  2. ⚠️ [SEND_MESSAGE] Encryption not initialized - will send unencrypted message
  3. 🔄 [SEND_MESSAGE] Generated tempId: 45fe1114-e85d-4c12-8e84-f0d63d668fc5
  4. 📝 [SEND_MESSAGE] Processing draft conversation: draft-46f2c6db-d796-4713-9415-0eb57bb496d4-1754491804255
  5. 👤 [SEND_MESSAGE] Extracted userId from draft: 46f2c6db-d796-4713-9415-0eb57bb496d4
  6. 🔄 [SEND_MESSAGE] Creating real conversation via socket...
  7. ✅ [SEND_MESSAGE] Conversation creation result: {id: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375, type: DIRECT, name: null, createdAt: 2025-08-04T04:56:02.038Z, updatedAt: 2025-08-06T14:38:48.275Z}
  8. 🎯 [SEND_MESSAGE] Using real conversation ID: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375
  9. 🔄 [SEND_MESSAGE] Converted draft to real conversation in Redux
  10. 🔐 [SEND_MESSAGE] Initializing encryption session for new conversation...
  11. 🔐 Initializing session for conversation: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375
  12. 🔐 ❌ API error during session initialization: SyntaxError: Cannot create a key using the specified key usages.
  13. 🔐 ❌ Failed to initialize session: SyntaxError: Cannot create a key using the specified key usages.
  14. 🔐 [SEND_MESSAGE] ❌ Failed to initialize encryption session: SyntaxError: Cannot create a key using the specified key usages.
  15. 🟡 [SEND_MESSAGE] Skipping Redux store for real conversation
  16. 🟡 [SEND_MESSAGE] Adding optimistic message to RTK Query cache...
  17. 🔶 [RTK_CACHE] Content: Encryption session test message 🔐
  18. 🟡 [SEND_MESSAGE] ✅ Added optimistic message to RTK Query cache
  19. 🟡 [SEND_MESSAGE] ✅ Optimistic message setup complete
  20. 🟡 [SEND_MESSAGE] TempId: 45fe1114-e85d-4c12-8e84-f0d63d668fc5
  21. 🟡 [SEND_MESSAGE] Conversation: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375
  22. 🔐 [SEND_MESSAGE] Encrypting message...
  23. 🔐 Encrypting message for conversation: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375
  24. 🔐 ❌ Failed to encrypt message: Error: Session not found for conversation
    at SignalProtocol.encryptMessage (http://localhost:5000/src/utils/signalProtocol.ts:94:13)
    at http://localhost:5000/src/contexts/EncryptionContext.tsx:139:64
    at http://localhost:5000/src/contexts/SocketContext.tsx?t=1754491604585:461:38
    at async handleSubmit (http://localhost:5000/src/components/Chat/MessageInput.tsx?t=1754491604585:60:7)
  25. 🔐 [SEND_MESSAGE] ❌ Failed to encrypt message: Error: Session not found for conversation
    at SignalProtocol.encryptMessage (http://localhost:5000/src/utils/signalProtocol.ts:94:13)
    at http://localhost:5000/src/contexts/EncryptionContext.tsx:139:64
    at http://localhost:5000/src/contexts/SocketContext.tsx?t=1754491604585:461:38
    at async handleSubmit (http://localhost:5000/src/components/Chat/MessageInput.tsx?t=1754491604585:60:7)
  26. 🔐 [SEND_MESSAGE] Error details: {message: Session not found for conversation, conversationId: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375, contentLength: 34}
  27. 📡 [SEND_MESSAGE] Falling back to unencrypted message...
  28. 📡 [SEND_MESSAGE] ✅ Unencrypted message emitted to socket
  29. 🔵 [NEW_MESSAGE] Received new_message event: {id: cfc568d6-1371-4b7b-98e6-4c804524fdef, conversationId: 46d0df0c-1bce-4400-8cd0-f7f1d74b9375, sender: Object, content: Encryption session test message 🔐, messageType: TEXT}
  30. 🔵 [NEW_MESSAGE] Content: Encryption session test message 🔐
  31. 🔶 [RTK_CACHE] Content: Encryption session test message 🔐
  32. 🔶 [RTK_CACHE] Searching for content: Encryption session test message 🔐
  33. 🔶 [RTK_CACHE] Cache[16]: ID=db4de890-dd94-4414-a34d-8774181f9d79, content="Encryption fix test message 🔐", isOptimistic=undefined, sender=78bb8aa7-b130-41bb-bb02-123ad01432bc

[[ATTACHMENT|../test-results/messaging-encryption-sessi-1ce68-and-send-encrypted-messages-chromium/test-failed-1.png]]

[[ATTACHMENT|../test-results/messaging-encryption-sessi-1ce68-and-send-encrypted-messages-chromium/video.webm]]

[[ATTACHMENT|../test-results/messaging-encryption-sessi-1ce68-and-send-encrypted-messages-chromium/error-context.md]]
]]>
</system-out>
<system-err>
<![CDATA[❌ Test failed: locator.isVisible: Error: strict mode violation: locator('text="Encryption session test message 🔐"') resolved to 2 elements:
    1) <p class="text-sm text-gray-600 truncate mt-1">…</p> aka getByRole('button', { name: '? Unknown User less than a' })
    2) <div class="text-sm leading-relaxed">Encryption session test message 🔐</div> aka getByTestId('message-list').getByText('Encryption session test')

Call log:
[2m    - checking visibility of locator('text="Encryption session test message 🔐"')[22m

    at [90m/home/<USER>/chatapp/[39me2e/tests/messaging/encryption-session-fix-test.spec.ts:166:101
    at [90m/home/<USER>/chatapp/[39me2e/tests/messaging/encryption-session-fix-test.spec.ts:162:18 {
  [[32mSymbol(step)[39m]: {
    stepId: [32m'pw:api@35'[39m,
    location: {
      file: [32m'/home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts'[39m,
      line: [33m166[39m,
      column: [33m101[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m`Is visible locator('text="Encryption session test message 🔐"')`[39m,
    apiName: [32m'locator.isVisible'[39m,
    params: {
      selector: [32m'text="Encryption session test message 🔐"'[39m,
      strict: [33mtrue[39m
    },
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@35'[39m,
      skip: [36m[Function (anonymous)][39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1754491812400[39m,
    error: {
      message: [32m`Error: locator.isVisible: Error: strict mode violation: locator('text="Encryption session test message 🔐"') resolved to 2 elements:\n`[39m +
        [32m`    1) <p class="text-sm text-gray-600 truncate mt-1">…</p> aka getByRole('button', { name: '? Unknown User less than a' })\n`[39m +
        [32m`    2) <div class="text-sm leading-relaxed">Encryption session test message 🔐</div> aka getByTestId('message-list').getByText('Encryption session test')\n`[39m +
        [32m'\n'[39m +
        [32m'Call log:\n'[39m +
        [32m`\x1B[2m    - checking visibility of locator('text="Encryption session test message 🔐"')\x1B[22m\n`[39m,
      stack: [32m`Error: locator.isVisible: Error: strict mode violation: locator('text="Encryption session test message 🔐"') resolved to 2 elements:\n`[39m +
        [32m`    1) <p class="text-sm text-gray-600 truncate mt-1">…</p> aka getByRole('button', { name: '? Unknown User less than a' })\n`[39m +
        [32m`    2) <div class="text-sm leading-relaxed">Encryption session test message 🔐</div> aka getByTestId('message-list').getByText('Encryption session test')\n`[39m +
        [32m'\n'[39m +
        [32m'Call log:\n'[39m +
        [32m`\x1B[2m    - checking visibility of locator('text="Encryption session test message 🔐"')\x1B[22m\n`[39m +
        [32m'\n'[39m +
        [32m'    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts:166:101\n'[39m +
        [32m'    at /home/<USER>/chatapp/e2e/tests/messaging/encryption-session-fix-test.spec.ts:162:18'[39m,
      cause: [90mundefined[39m
    }
  }
}
]]>
</system-err>
</testcase>
</testsuite>
</testsuites>